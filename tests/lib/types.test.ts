import { describe, expect, it } from "vitest";
import type { AppBindings, CleanCodeRequest, CleanCodeResponse } from "@/lib/types";

describe("Type Definitions", () => {
  describe("CleanCodeRequest", () => {
    it("should accept valid CleanCodeRequest", () => {
      const validRequest: CleanCodeRequest = {
        code: "function test() { console.log('hello'); }",
        imageUrl: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      };

      expect(validRequest.code).toBe("function test() { console.log('hello'); }");
      expect(validRequest.imageUrl).toContain("data:image/png;base64,");
    });

    it("should have required properties", () => {
      // This test ensures the type has the expected shape
      const request: CleanCodeRequest = {
        code: "",
        imageUrl: "",
      };

      expect(typeof request.code).toBe("string");
      expect(typeof request.imageUrl).toBe("string");
    });
  });

  describe("CleanCodeResponse", () => {
    it("should accept valid CleanCodeResponse with suggestions", () => {
      const validResponse: CleanCodeResponse = {
        cleanCode: "function optimizedTest() { console.log('hello'); }",
        suggestions: ["Use const instead of let", "Add type annotations"],
      };

      expect(validResponse.cleanCode).toBe("function optimizedTest() { console.log('hello'); }");
      expect(Array.isArray(validResponse.suggestions)).toBe(true);
      expect(validResponse.suggestions?.length).toBe(2);
    });

    it("should accept valid CleanCodeResponse without suggestions", () => {
      const validResponse: CleanCodeResponse = {
        cleanCode: "function optimizedTest() { console.log('hello'); }",
      };

      expect(validResponse.cleanCode).toBe("function optimizedTest() { console.log('hello'); }");
      expect(validResponse.suggestions).toBeUndefined();
    });

    it("should accept CleanCodeResponse with empty suggestions array", () => {
      const validResponse: CleanCodeResponse = {
        cleanCode: "function optimizedTest() { console.log('hello'); }",
        suggestions: [],
      };

      expect(validResponse.cleanCode).toBe("function optimizedTest() { console.log('hello'); }");
      expect(Array.isArray(validResponse.suggestions)).toBe(true);
      expect(validResponse.suggestions?.length).toBe(0);
    });

    it("should have required cleanCode property", () => {
      const response: CleanCodeResponse = {
        cleanCode: "",
      };

      expect(typeof response.cleanCode).toBe("string");
    });
  });

  describe("AppBindings", () => {
    it("should define logger in Variables", () => {
      // This is more of a compile-time test to ensure the type structure is correct
      // We can't directly test the type at runtime, but we can test its usage
      
      // Mock a context that would use AppBindings
      const mockContext = {
        var: {
          logger: {
            info: vi.fn(),
            error: vi.fn(),
            debug: vi.fn(),
            warn: vi.fn(),
          },
        },
      };

      expect(mockContext.var.logger).toBeDefined();
      expect(typeof mockContext.var.logger.info).toBe("function");
      expect(typeof mockContext.var.logger.error).toBe("function");
      expect(typeof mockContext.var.logger.debug).toBe("function");
      expect(typeof mockContext.var.logger.warn).toBe("function");
    });
  });

  describe("Type compatibility", () => {
    it("should work with JSON serialization", () => {
      const request: CleanCodeRequest = {
        code: "function test() {}",
        imageUrl: "data:image/png;base64,test",
      };

      const response: CleanCodeResponse = {
        cleanCode: "function optimizedTest() {}",
        suggestions: ["suggestion 1"],
      };

      // Should be serializable to JSON
      const serializedRequest = JSON.stringify(request);
      const serializedResponse = JSON.stringify(response);

      expect(typeof serializedRequest).toBe("string");
      expect(typeof serializedResponse).toBe("string");

      // Should be deserializable from JSON
      const deserializedRequest = JSON.parse(serializedRequest) as CleanCodeRequest;
      const deserializedResponse = JSON.parse(serializedResponse) as CleanCodeResponse;

      expect(deserializedRequest.code).toBe(request.code);
      expect(deserializedRequest.imageUrl).toBe(request.imageUrl);
      expect(deserializedResponse.cleanCode).toBe(response.cleanCode);
      expect(deserializedResponse.suggestions).toEqual(response.suggestions);
    });
  });
});
