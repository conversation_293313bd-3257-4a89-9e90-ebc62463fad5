import { describe, expect, it } from "vitest";
import createApp, { createRouter } from "@/lib/create-app";
import { testRequest } from "../utils/test-helpers";

describe("Create App Utility", () => {
  describe("createApp", () => {
    it("should create a Hono app instance", () => {
      const app = createApp();
      
      expect(app).toBeDefined();
      expect(typeof app.fetch).toBe("function");
      expect(typeof app.get).toBe("function");
      expect(typeof app.post).toBe("function");
      expect(typeof app.use).toBe("function");
    });

    it("should handle 404 for non-existent routes", async () => {
      const app = createApp();
      
      const response = await testRequest(app, "/non-existent");
      
      expect(response.status).toBe(404);
    });

    it("should serve favicon", async () => {
      const app = createApp();
      
      const response = await testRequest(app, "/favicon.ico");
      
      // Should not be 404 (favicon middleware should handle it)
      expect(response.status).not.toBe(404);
    });

    it("should apply auth middleware to /clean-code route", async () => {
      const app = createApp();
      
      // Add a test route to /clean-code
      app.post("/clean-code", (c) => c.json({ message: "success" }));
      
      // Request without auth should be rejected
      const response = await testRequest(app, "/clean-code", {
        method: "POST",
      });
      
      expect(response.status).toBe(401);
    });

    it("should not apply auth middleware to other routes", async () => {
      const app = createApp();
      
      // Add a test route
      app.get("/test", (c) => c.json({ message: "success" }));
      
      // Request without auth should succeed
      const response = await testRequest(app, "/test");
      
      expect(response.status).toBe(200);
    });
  });

  describe("createRouter", () => {
    it("should create a Hono router instance", () => {
      const router = createRouter();
      
      expect(router).toBeDefined();
      expect(typeof router.fetch).toBe("function");
      expect(typeof router.get).toBe("function");
      expect(typeof router.post).toBe("function");
      expect(typeof router.use).toBe("function");
    });

    it("should create independent router instances", () => {
      const router1 = createRouter();
      const router2 = createRouter();
      
      expect(router1).not.toBe(router2);
    });

    it("should allow adding routes to router", async () => {
      const router = createRouter();
      
      router.get("/test", (c) => c.json({ message: "router test" }));
      
      const response = await testRequest(router, "/test");
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toEqual({ message: "router test" });
    });
  });

  describe("Middleware integration", () => {
    it("should include CORS middleware", async () => {
      const app = createApp();
      
      app.get("/test", (c) => c.json({ message: "test" }));
      
      const response = await testRequest(app, "/test");
      
      // CORS middleware should add headers
      expect(response.headers.get("content-type")).toContain("application/json");
    });

    it("should include logger middleware", async () => {
      const app = createApp();
      
      app.get("/test", (c) => {
        // Logger should be available in context
        expect(c.var.logger).toBeDefined();
        expect(typeof c.var.logger.info).toBe("function");
        return c.json({ message: "test" });
      });
      
      const response = await testRequest(app, "/test");
      
      expect(response.status).toBe(200);
    });
  });

  describe("Error handling", () => {
    it("should handle errors with onError middleware", async () => {
      const app = createApp();
      
      app.get("/error", () => {
        throw new Error("Test error");
      });
      
      const response = await testRequest(app, "/error");
      
      // Should handle the error gracefully
      expect(response.status).toBe(500);
    });

    it("should handle async errors", async () => {
      const app = createApp();
      
      app.get("/async-error", async () => {
        throw new Error("Async test error");
      });
      
      const response = await testRequest(app, "/async-error");
      
      // Should handle the async error gracefully
      expect(response.status).toBe(500);
    });
  });
});
