import { describe, expect, it, beforeEach, vi } from "vitest";
import app from "@/app";
import { testRequest, generateTestToken, createMockCleanCodeRequest } from "../utils/test-helpers";

describe("Application Integration Tests", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("Public routes", () => {
    it("should access index route without authentication", async () => {
      const response = await testRequest(app, "/");
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toEqual({ message: "Hello Hono!" });
    });

    it("should access auth token route without authentication", async () => {
      const response = await testRequest(app, "/auth/token");
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty("token");
    });

    it("should access debug error route without authentication", async () => {
      const response = await testRequest(app, "/error");
      
      expect(response.status).toBe(500);
    });
  });

  describe("Protected routes", () => {
    it("should reject clean-code request without authentication", async () => {
      const mockRequest = createMockCleanCodeRequest();
      
      const response = await testRequest(app, "/clean-code", {
        method: "POST",
        body: mockRequest,
      });
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Unauthorized" });
    });

    it("should allow clean-code request with valid authentication", async () => {
      const token = await generateTestToken();
      const mockRequest = createMockCleanCodeRequest();
      
      const response = await testRequest(app, "/clean-code", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: mockRequest,
      });
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty("cleanCode");
      expect(data).toHaveProperty("suggestions");
    });
  });

  describe("Full authentication flow", () => {
    it("should complete full flow: get token -> use token -> access protected resource", async () => {
      // Step 1: Get a token
      const tokenResponse = await testRequest(app, "/auth/token");
      expect(tokenResponse.status).toBe(200);
      
      const tokenData = await tokenResponse.json();
      const token = tokenData.token;
      
      // Step 2: Use the token to access protected resource
      const mockRequest = createMockCleanCodeRequest();
      
      const protectedResponse = await testRequest(app, "/clean-code", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        body: mockRequest,
      });
      
      expect(protectedResponse.status).toBe(200);
      
      const protectedData = await protectedResponse.json();
      expect(protectedData).toHaveProperty("cleanCode");
      expect(protectedData).toHaveProperty("suggestions");
    });
  });

  describe("Error handling", () => {
    it("should handle 404 for non-existent routes", async () => {
      const response = await testRequest(app, "/non-existent-route");
      
      expect(response.status).toBe(404);
    });

    it("should handle method not allowed", async () => {
      // Try POST on a GET-only route
      const response = await testRequest(app, "/", {
        method: "POST",
      });
      
      expect(response.status).toBe(404);
    });

    it("should handle invalid JSON in clean-code request", async () => {
      const token = await generateTestToken();
      
      const response = await testRequest(app, "/clean-code", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: "invalid json",
      });
      
      expect(response.status).toBe(500);
    });
  });

  describe("CORS and headers", () => {
    it("should include CORS headers", async () => {
      const response = await testRequest(app, "/");
      
      // Check for CORS headers (these would be set by the honoCors middleware)
      // The exact headers depend on the CORS configuration
      expect(response.headers.get("content-type")).toContain("application/json");
    });
  });

  describe("Content-Type handling", () => {
    it("should handle requests with correct content-type", async () => {
      const token = await generateTestToken();
      const mockRequest = createMockCleanCodeRequest();
      
      const response = await testRequest(app, "/clean-code", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: mockRequest,
      });
      
      expect(response.status).toBe(200);
    });
  });
});
