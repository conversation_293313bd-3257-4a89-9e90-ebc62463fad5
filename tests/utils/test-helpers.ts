import type { Hono } from "hono";
import { sign } from "hono/jwt";

/**
 * Helper function to make requests to Hono app
 */
export async function testRequest(
  app: Hono,
  path: string,
  options: {
    method?: string;
    headers?: Record<string, string>;
    body?: any;
  } = {},
) {
  const { method = "GET", headers = {}, body } = options;
  
  const request = new Request(`http://localhost${path}`, {
    method,
    headers: {
      "Content-Type": "application/json",
      ...headers,
    },
    body: body ? JSON.stringify(body) : undefined,
  });

  return await app.fetch(request);
}

/**
 * Generate a valid JWT token for testing
 */
export async function generateTestToken(secret: string = "test-jwt-secret-key-for-testing") {
  return await sign(
    { exp: Math.floor(Date.now() / 1000) + 15 * 60 },
    secret,
  );
}

/**
 * Generate an expired JWT token for testing
 */
export async function generateExpiredToken(secret: string = "test-jwt-secret-key-for-testing") {
  return await sign(
    { exp: Math.floor(Date.now() / 1000) - 60 }, // Expired 1 minute ago
    secret,
  );
}

/**
 * Create a mock base64 image data URL
 */
export function createMockImageDataUrl(): string {
  // Simple base64 encoded 1x1 pixel PNG
  const base64Data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
  return `data:image/png;base64,${base64Data}`;
}

/**
 * Mock clean code request payload
 */
export function createMockCleanCodeRequest() {
  return {
    code: "function test() { console.log('hello'); }",
    imageUrl: createMockImageDataUrl(),
  };
}
