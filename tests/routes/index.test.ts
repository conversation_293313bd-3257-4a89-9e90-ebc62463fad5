import { describe, expect, it } from "vitest";
import indexRoutes from "@/routes/index";
import { testRequest } from "../utils/test-helpers";

describe("Index Routes", () => {
  describe("GET /", () => {
    it("should return hello message", async () => {
      const response = await testRequest(indexRoutes, "/");
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toEqual({
        message: "Hello Hono!",
      });
    });

    it("should have correct content type", async () => {
      const response = await testRequest(indexRoutes, "/");
      
      expect(response.headers.get("content-type")).toContain("application/json");
    });
  });

  describe("404 handling", () => {
    it("should handle non-existent routes", async () => {
      const response = await testRequest(indexRoutes, "/non-existent");
      
      expect(response.status).toBe(404);
    });
  });
});
