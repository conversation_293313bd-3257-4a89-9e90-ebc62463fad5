import { describe, expect, it, vi } from "vitest";
import { Hono } from "hono";
import type { AppBindings } from "@/lib/types";
import { testRequest } from "../utils/test-helpers";

// Create a test app with logger middleware for debug routes
const createTestApp = () => {
  const app = new Hono<AppBindings>();

  // Add a mock logger to the context
  app.use("*", async (c, next) => {
    c.set("logger", {
      info: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
    } as any);
    await next();
  });

  app.get("/error", (c) => {
    c.var.logger.info("Log here!");
    c.var.logger.debug("Only visible when debug level is enabled!");
    throw new Error("Internal Server Error!");
  });

  return app;
};

describe("Debug Routes", () => {
  describe("GET /error", () => {
    it("should throw an internal server error", async () => {
      const app = createTestApp();
      const response = await testRequest(app, "/error");

      // The route should throw an error, which should be handled by error middleware
      // In a basic Hono router without error handling, this would be a 500
      expect(response.status).toBe(500);
    });

    it("should handle the error gracefully", async () => {
      const app = createTestApp();
      const response = await testRequest(app, "/error");

      // Ensure the response is still valid JSON or text
      const contentType = response.headers.get("content-type");
      expect(contentType).toBeTruthy();

      // The response should not be empty
      const text = await response.text();
      expect(text.length).toBeGreaterThan(0);
    });
  });
});
