import { describe, expect, it, vi, beforeEach } from "vitest";
import { <PERSON><PERSON> } from "hono";
import type { AppBindings, CleanCodeRequest, CleanCodeResponse } from "@/lib/types";
import Anthropic from "@anthropic-ai/sdk";
import env from "@/env";
import { prompt } from "@/lib/prompt";
import { testRequest, createMockCleanCodeRequest } from "../utils/test-helpers";

// Get the mocked Anthropic constructor
const MockedAnthropic = vi.mocked(Anthropic);

// Create a test app with logger middleware for clean-code routes
const createTestApp = () => {
  const app = new Hono<AppBindings>();

  // Add a mock logger to the context
  app.use("*", async (c, next) => {
    c.set("logger", {
      info: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
    } as any);
    await next();
  });

  // Add the clean-code route
  app.post("/", async (c) => {
    const { code, imageUrl } = await c.req.json<CleanCodeRequest>();

    try {
      const client = new Anthropic({ apiKey: env.ANTHROPIC_API_KEY });
      const base64Image = imageUrl.split(",")[1] || "";

      c.var.logger.info({
        event: "anthropic_api_call",
        imageDataLength: base64Image.length,
      }, "Calling Anthropic API");

      const res = await client.messages.create({
        model: "claude-3-5-sonnet-latest",
        max_tokens: 4000,
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: `${prompt}\nClean and optimize this code:\n${code}`,
              },
              {
                type: "image",
                source: {
                  type: "base64",
                  media_type: "image/png",
                  data: base64Image,
                },
              },
            ],
          },
        ],
      });

      c.var.logger.info({
        event: "anthropic_api_response",
        inputTokens: res.usage.input_tokens,
        outputTokens: res.usage.output_tokens,
        contentBlocks: res.content.length,
      }, "Received response from Anthropic API");

      const textPart = res.content.find((c: any) => c.type === "text") as any;
      const cleanCode = textPart?.text || "";

      const response: CleanCodeResponse = {
        cleanCode,
        suggestions: [],
      };

      return c.json(response, 200);
    }
    catch (error) {
      const errorMessage
        = error instanceof Error ? error.message : "Unknown error";

      const resp: CleanCodeResponse = {
        cleanCode: "",
        suggestions: [errorMessage],
      };
      return c.json(resp, 500);
    }
  });

  return app;
};

describe("Clean Code Routes", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("POST /", () => {
    it("should successfully clean code with valid request", async () => {
      const app = createTestApp();
      const mockRequest = createMockCleanCodeRequest();

      const response = await testRequest(app, "/", {
        method: "POST",
        body: mockRequest,
      });

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data).toHaveProperty("cleanCode");
      expect(data).toHaveProperty("suggestions");
      expect(Array.isArray(data.suggestions)).toBe(true);
      expect(typeof data.cleanCode).toBe("string");
      expect(data.cleanCode.length).toBeGreaterThan(0);
    });

    it("should call Anthropic API with correct parameters", async () => {
      const app = createTestApp();
      const mockRequest = createMockCleanCodeRequest();

      await testRequest(app, "/", {
        method: "POST",
        body: mockRequest,
      });

      // Verify Anthropic was instantiated
      expect(MockedAnthropic).toHaveBeenCalledWith({
        apiKey: "test-anthropic-api-key",
      });

      // Get the mocked instance
      const mockInstance = MockedAnthropic.mock.results[0].value;

      // Verify messages.create was called
      expect(mockInstance.messages.create).toHaveBeenCalledWith({
        model: "claude-3-5-sonnet-latest",
        max_tokens: 4000,
        messages: [
          {
            role: "user",
            content: [
              {
                type: "text",
                text: expect.stringContaining(mockRequest.code),
              },
              {
                type: "image",
                source: {
                  type: "base64",
                  media_type: "image/png",
                  data: expect.any(String),
                },
              },
            ],
          },
        ],
      });
    });

    it("should handle missing code field", async () => {
      const app = createTestApp();
      const invalidRequest = {
        imageUrl: "data:image/png;base64,test",
        // missing code field
      };

      const response = await testRequest(app, "/", {
        method: "POST",
        body: invalidRequest,
      });

      // Should handle the error gracefully
      expect(response.status).toBe(500);

      const data = await response.json();
      expect(data).toHaveProperty("cleanCode", "");
      expect(data).toHaveProperty("suggestions");
      expect(Array.isArray(data.suggestions)).toBe(true);
      expect(data.suggestions.length).toBeGreaterThan(0);
    });

    it("should handle missing imageUrl field", async () => {
      const app = createTestApp();
      const invalidRequest = {
        code: "function test() {}",
        // missing imageUrl field
      };

      const response = await testRequest(app, "/", {
        method: "POST",
        body: invalidRequest,
      });

      // Should handle the error gracefully
      expect(response.status).toBe(500);

      const data = await response.json();
      expect(data).toHaveProperty("cleanCode", "");
      expect(data).toHaveProperty("suggestions");
      expect(Array.isArray(data.suggestions)).toBe(true);
    });

    it("should handle Anthropic API errors", async () => {
      // Mock Anthropic to throw an error
      const mockInstance = {
        messages: {
          create: vi.fn().mockRejectedValue(new Error("API Error")),
        },
      };
      MockedAnthropic.mockImplementation(() => mockInstance as any);

      const app = createTestApp();
      const mockRequest = createMockCleanCodeRequest();

      const response = await testRequest(app, "/", {
        method: "POST",
        body: mockRequest,
      });

      expect(response.status).toBe(500);

      const data = await response.json();
      expect(data).toEqual({
        cleanCode: "",
        suggestions: ["API Error"],
      });
    });

    it("should handle invalid JSON request", async () => {
      const app = createTestApp();
      const response = await testRequest(app, "/", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: "invalid json",
      });

      expect(response.status).toBe(500);
    });

    it("should extract base64 data from data URL correctly", async () => {
      const app = createTestApp();
      const base64Data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";
      const mockRequest = {
        code: "function test() {}",
        imageUrl: `data:image/png;base64,${base64Data}`,
      };

      await testRequest(app, "/", {
        method: "POST",
        body: mockRequest,
      });

      const mockInstance = MockedAnthropic.mock.results[0].value;
      const callArgs = mockInstance.messages.create.mock.calls[0][0];

      // Check that the base64 data was extracted correctly
      const imageContent = callArgs.messages[0].content[1];
      expect(imageContent.source.data).toBe(base64Data);
    });

    it("should handle empty base64 data", async () => {
      const app = createTestApp();
      const mockRequest = {
        code: "function test() {}",
        imageUrl: "data:image/png;base64,", // Empty base64 data
      };

      const response = await testRequest(app, "/", {
        method: "POST",
        body: mockRequest,
      });

      expect(response.status).toBe(200);

      const mockInstance = MockedAnthropic.mock.results[0].value;
      const callArgs = mockInstance.messages.create.mock.calls[0][0];

      // Should handle empty base64 data
      const imageContent = callArgs.messages[0].content[1];
      expect(imageContent.source.data).toBe("");
    });
  });
});
