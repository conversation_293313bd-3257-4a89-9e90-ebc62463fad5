import { describe, expect, it } from "vitest";
import { verify } from "hono/jwt";
import authRoutes from "@/routes/auth";
import { testRequest } from "../utils/test-helpers";

describe("Auth Routes", () => {
  describe("GET /token", () => {
    it("should generate a valid JWT token", async () => {
      const response = await testRequest(authRoutes, "/token");
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toHaveProperty("token");
      expect(typeof data.token).toBe("string");
      expect(data.token.length).toBeGreaterThan(0);
    });

    it("should generate a token that can be verified", async () => {
      const response = await testRequest(authRoutes, "/token");
      const data = await response.json();
      
      // Verify the token can be decoded
      const payload = await verify(data.token, "test-jwt-secret-key-for-testing");
      
      expect(payload).toHaveProperty("exp");
      expect(typeof payload.exp).toBe("number");
      
      // Check that expiration is in the future (15 minutes from now)
      const now = Math.floor(Date.now() / 1000);
      const expectedExp = now + 15 * 60;
      
      // Allow for small time differences (within 5 seconds)
      expect(payload.exp).toBeGreaterThan(now);
      expect(payload.exp).toBeLessThanOrEqual(expectedExp + 5);
    });

    it("should have correct content type", async () => {
      const response = await testRequest(authRoutes, "/token");
      
      expect(response.headers.get("content-type")).toContain("application/json");
    });

    it("should generate different tokens on subsequent calls", async () => {
      const response1 = await testRequest(authRoutes, "/token");
      const response2 = await testRequest(authRoutes, "/token");
      
      const data1 = await response1.json();
      const data2 = await response2.json();
      
      // Tokens should be different (due to different timestamps)
      expect(data1.token).not.toBe(data2.token);
    });
  });
});
