import { describe, expect, it, vi } from "vitest";
import { Hono } from "hono";
import type { AppBindings } from "@/lib/types";
import { auth } from "@/middlewares/auth";
import { testRequest, generateTestToken, generateExpiredToken } from "../utils/test-helpers";

describe("Auth Middleware", () => {
  // Create a test app with the auth middleware
  const createTestApp = () => {
    const app = new Hono<AppBindings>();
    
    // Add a mock logger to the context
    app.use("*", async (c, next) => {
      c.set("logger", {
        info: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
      } as any);
      await next();
    });
    
    app.use("/protected", auth);
    app.get("/protected", (c) => c.json({ message: "Protected resource" }));
    
    return app;
  };

  describe("Valid token", () => {
    it("should allow access with valid Bearer token", async () => {
      const app = createTestApp();
      const token = await generateTestToken();
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data).toEqual({ message: "Protected resource" });
    });
  });

  describe("Missing Authorization header", () => {
    it("should return 401 when Authorization header is missing", async () => {
      const app = createTestApp();
      
      const response = await testRequest(app, "/protected");
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Unauthorized" });
    });
  });

  describe("Invalid Authorization header format", () => {
    it("should return 401 when Authorization header doesn't start with Bearer", async () => {
      const app = createTestApp();
      const token = await generateTestToken();
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: `Basic ${token}`,
        },
      });
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Unauthorized" });
    });

    it("should return 401 when Authorization header is just 'Bearer'", async () => {
      const app = createTestApp();
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: "Bearer",
        },
      });
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Unauthorized" });
    });

    it("should return 401 when Authorization header is empty", async () => {
      const app = createTestApp();
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: "",
        },
      });
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Unauthorized" });
    });
  });

  describe("Invalid tokens", () => {
    it("should return 401 for expired token", async () => {
      const app = createTestApp();
      const expiredToken = await generateExpiredToken();
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: `Bearer ${expiredToken}`,
        },
      });
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Invalid token" });
    });

    it("should return 401 for malformed token", async () => {
      const app = createTestApp();
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: "Bearer invalid.token.here",
        },
      });
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Invalid token" });
    });

    it("should return 401 for token with wrong secret", async () => {
      const app = createTestApp();
      const tokenWithWrongSecret = await generateTestToken("wrong-secret");
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: `Bearer ${tokenWithWrongSecret}`,
        },
      });
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Invalid token" });
    });

    it("should return 401 for completely invalid token string", async () => {
      const app = createTestApp();
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: "Bearer not-a-jwt-token",
        },
      });
      
      expect(response.status).toBe(401);
      
      const data = await response.json();
      expect(data).toEqual({ error: "Invalid token" });
    });
  });

  describe("Error logging", () => {
    it("should log authentication errors", async () => {
      const app = createTestApp();
      
      const response = await testRequest(app, "/protected", {
        headers: {
          Authorization: "Bearer invalid-token",
        },
      });
      
      expect(response.status).toBe(401);
      
      // Note: In a real test, you might want to verify that the logger.error was called
      // This would require more sophisticated mocking of the logger
    });
  });
});
