import { beforeAll, beforeEach, vi } from "vitest";

// Store original environment
const originalEnv = process.env;

// Mock environment variables
beforeAll(() => {
  // Set test environment variables
  process.env = {
    ...originalEnv,
    NODE_ENV: "test",
    JWT_SECRET: "test-jwt-secret-key-for-testing",
    ANTHROPIC_API_KEY: "test-anthropic-api-key",
    LOG_LEVEL: "silent",
    PORT: "3001",
  };
});

beforeEach(() => {
  // Reset modules to ensure fresh imports
  vi.resetModules();
});

// Mock Anthropic SDK
vi.mock("@anthropic-ai/sdk", () => {
  return {
    default: vi.fn().mockImplementation(() => ({
      messages: {
        create: vi.fn().mockResolvedValue({
          content: [
            {
              type: "text",
              text: "// Cleaned and optimized code\nfunction cleanCode() {\n  return 'optimized';\n}",
            },
          ],
          usage: {
            input_tokens: 100,
            output_tokens: 50,
          },
        }),
      },
    })),
  };
});
